// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
  id: json['Id'] as String?,
  name: json['Name'] as String? ?? '',
  groupId: json['GroupId'] as String? ?? '',
  parentIdList:
      (json['ParentIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  defaultenum: (json['Defaultenum'] as num?)?.toInt() ?? 2,
  isVisible: json['isVisible'] as bool? ?? false,
);

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
  'Id': instance.id,
  'Name': instance.name,
  'GroupId': instance.groupId,
  'ParentIdList': instance.parentIdList,
  'Defaultenum': instance.defaultenum,
  'isVisible': instance.isVisible,
};

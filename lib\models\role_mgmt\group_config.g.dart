// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupConfig _$GroupConfigFromJson(Map<String, dynamic> json) => GroupConfig(
  id: json['Id'] as String?,
  name: json['Name'] as String? ?? '',
  groupTypeenum: (json['GroupTypeenum'] as num?)?.toInt() ?? 1,
  defaultenum: (json['Defaultenum'] as num?)?.toInt() ?? 2,
  roleList:
      (json['RoleList'] as List<dynamic>?)
          ?.map((e) => Role.fromJson(e as Map<String, dynamic>))
          .toList() ??
      [],
);

Map<String, dynamic> _$GroupConfigToJson(GroupConfig instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Name': instance.name,
      'GroupTypeenum': instance.groupTypeenum,
      'Defaultenum': instance.defaultenum,
      'RoleList': instance.roleList?.map((e) => e.toJson()).toList(),
    };

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/role_mgmt/group_config.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
// import 'package:octasync_client/views/admin/role_auth_management/create_group_dialog.dart';
// import 'package:octasync_client/views/admin/role_auth_management/create_role_dialog.dart';
// import 'package:octasync_client/views/admin/role_auth_management/dialog_type_enum.dart';

class _GroupListConstants {
  static const int defaultPageSize = 9999;
  static const String loadingMessage = '加载中...';
  static const String emptyDataMessage = '暂无数据';
  // static const String noSearchResultMessage = '暂无数据';
  static const String retryButtonText = '重试';
  // static const String loadErrorPrefix = '加载数据失败: ';
}

class GroupList extends StatefulWidget {
  /// 搜索关键字
  final String? keywords;

  /// 点击分组项
  final void Function(Role role)? onItemTap;

  /// 编辑分组
  final void Function(GroupConfig group)? editGroup;

  /// 编辑角色
  final void Function(Role role)? editRole;

  const GroupList({super.key, this.keywords, this.onItemTap, this.editGroup, this.editRole});

  @override
  State<GroupList> createState() => GroupListState();
}

class GroupListState extends State<GroupList> {
  // final GlobalKey<CreateGroupDialogState> _createGroupDialogStateKey =
  //     GlobalKey<CreateGroupDialogState>();

  // final GlobalKey<CreateRoleDialogState> _createRoleDialogStateKey =
  //     GlobalKey<CreateRoleDialogState>();

  BorderRadius itemBorderRadius = BorderRadius.circular(AppRadiusSize.radius4);
  double iconSize = 16;

  bool _isLoading = false;
  List<GroupConfig> groups = [];

  List<Role> get roles => groups.expand((g) => g.roleList ?? []).cast<Role>().toList();

  Role? get checkedRole => roles.firstWhereOrNull((r) => r.id == _checkedRoleId);

  String? _errorMessage;

  String _checkedRoleId = '';

  @override
  void initState() {
    super.initState();

    getList();
  }

  @override
  void didUpdateWidget(covariant GroupList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当 widget 参数发生变化时，重新计算格式
    if (oldWidget.keywords != widget.keywords) {
      setState(() {
        // 触发重新构建以应用过滤
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsGeometry.symmetric(vertical: 10),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsetsGeometry.only(left: 16, right: 16, bottom: 10),
            // child: Row(
            //   children: [
            //     CreateGroupDialog(
            //       key: _createGroupDialogStateKey,
            //       onSuccess: () => getList(),
            //       child: AppButton(
            //         text: '创建分组',
            //         type: ButtonType.primary,
            //         onPressed: () {
            //           _createGroupDialogStateKey.currentState?.showCreateGroupDialog(
            //             context,
            //             type: DialogTypeEmun.create,
            //           );
            //         },
            //       ),
            //     ),
            //     SizedBox(width: 10),

            //     CreateRoleDialog(
            //       key: _createRoleDialogStateKey,
            //       onSuccess: () => getList(),
            //       // groups: groups,
            //       // roles: roles,
            //       child: AppButton(
            //         text: '创建角色',
            //         type: ButtonType.primary,
            //         onPressed: () {
            //           _createRoleDialogStateKey.currentState?.showCreateRoleDialog(
            //             context,
            //             type: DialogTypeEmun.create,
            //           );
            //         },
            //       ),
            //     ),

            //     Spacer(),
            //     AppButton(
            //       iconData: Icons.expand,
            //       size: ButtonSize.small,
            //       type: ButtonType.default_,
            //       onPressed: () {
            //         toggleAll();
            //       },
            //     ),
            //   ],
            // ),
          ),
          Expanded(child: _buildGroupList()),
        ],
      ),
    );
  }

  // 添加一个计算过滤后组的方法
  List<GroupConfig> getFilteredGroups() {
    if (widget.keywords == null || widget.keywords!.isEmpty) {
      return groups;
    }

    final keyword = widget.keywords!.toLowerCase();
    final filteredGroups = <GroupConfig>[];

    for (final group in groups) {
      // 检查组名称是否匹配
      final isGroupMatch = group.name?.toLowerCase().contains(keyword) ?? false;

      // 检查角色列表中是否有匹配项
      final matchedRoles =
          group.roleList?.where((role) {
            return role.name?.toLowerCase().contains(keyword) ?? false;
          }).toList() ??
          [];

      // 如果组名称匹配，或者有匹配的角色，则添加到结果中
      if (isGroupMatch || matchedRoles.isNotEmpty) {
        // 创建一个新的 GroupConfig 实例，只包含匹配的角色
        final newGroup = GroupConfig.fromJson(group.toJson());
        newGroup.roleList = matchedRoles;
        // 如果是组匹配，则展开组以显示子角色
        if (isGroupMatch) {
          newGroup.isExpanded = true;
        }
        filteredGroups.add(newGroup);
      }
    }

    return filteredGroups;
  }

  Future<void> getList() async {
    //放置重复请求
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await RoleAuthManagementApi.getList({});

      if (mounted) {
        setState(() {
          _isLoading = false;
          groups =
              (response as List)
                  .map((e) => GroupConfig.fromJson(e as Map<String, dynamic>))
                  .toList();

          if (roles.isNotEmpty && roles.any((r) => r.id == _checkedRoleId)) {
            //不需要刷新用户列表（节点中包含选中的节点）
          } else if (roles.isNotEmpty && !roles.any((r) => r.id == _checkedRoleId)) {
            //节点中不包含选中的节点，需要重新切换节点，并获取表格数据（删除已选中的节点）
            _checkedRoleId = roles[0].id!;
            widget.onItemTap?.call(roles[0]);
          }
        });
      }
    } catch (e) {
      _isLoading = true;
      _errorMessage = '获取数据失败：$e';
    }
  }

  void toggleAll() {
    setState(() {
      groups.forEach((g) => g.isExpanded = !g.isExpanded);
    });
  }

  Widget _buildGroupList() {
    final displayGroups = getFilteredGroups();

    // 错误状态
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: getList,
              child: const Text(_GroupListConstants.retryButtonText),
            ),
          ],
        ),
      );
    }

    // 加载状态
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              _GroupListConstants.loadingMessage,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ],
        ),
      );
    }

    // 空数据状态
    if (displayGroups.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(_GroupListConstants.emptyDataMessage, style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: displayGroups.length,
      // itemExtent: 80,
      itemBuilder: (BuildContext ctxt, int index) {
        var group = displayGroups[index];
        return Container(child: Column(children: [_buildGroup(context, group)]));
      },
    );
  }

  Widget _buildGroup(BuildContext context, GroupConfig group) {
    return Container(
      child: Column(
        children: [
          _buildItem(context, '${group.name}', isGroup: true, group: group),
          if (group.roleList != null && group.roleList!.isNotEmpty && group.isExpanded)
            ..._buildItems(context, group.roleList!),
        ],
      ),
    );
  }

  List<Widget> _buildItems(BuildContext context, List<Role> roleList) {
    List<Widget> result = [];
    for (var i = 0; i < roleList.length; i++) {
      result.add(
        _buildItem(
          context,
          roleList[i].name,
          role: roleList[i],
          onTap: () {
            setState(() {
              _checkedRoleId = roleList[i].id!;
            });
            widget.onItemTap?.call(roleList[i]);
          },
        ),
      );
    }

    return result;
  }

  Widget _buildItem(
    BuildContext context,
    String text, {
    bool isGroup = false,
    GroupConfig? group,
    Role? role,
    VoidCallback? onTap,
  }) {
    var isActive = isGroup == false && role != null && role.id == _checkedRoleId;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 1, horizontal: 16),
      width: double.infinity,
      height: 36,
      child: Material(
        color:
            isGroup
                ? context.background200
                : isActive
                ? context.background300
                : Colors.transparent,
        borderRadius: itemBorderRadius,
        child: InkWell(
          onTap: () {
            onTap?.call();
          },
          hoverColor: context.activeGrayColor.withValues(alpha: 0.5),
          splashColor: context.activeGrayColor,
          borderRadius: itemBorderRadius,
          child: Row(
            children: [
              isGroup
                  ? GestureDetector(
                    onTap: () {
                      setState(() {
                        group.isExpanded = !group.isExpanded;
                      });
                    },
                    child: AnimatedRotation(
                      key: UniqueKey(),
                      turns: group!.isExpanded ? 0 : -0.25,
                      duration: Duration(milliseconds: 300),
                      child: Icon(IconFont.mianxing_xiala, color: context.icon100, size: iconSize),
                    ),
                  )
                  : SizedBox(width: iconSize),
              Expanded(
                child: Text(
                  text,
                  style: TextStyle(color: isActive ? context.textPrimary : context.textSecondary),
                ),
              ),

              // 编辑、删除 按钮组
              GestureDetector(
                onTap: () {},
                child: AppDropdown(
                  placement: DropdownPlacement.bottomRight,
                  size: DropdownSize.small,
                  items: [
                    DropdownItem(text: '编辑', value: 'edit'),
                    DropdownItem(text: '删除', value: 'delete'),
                  ],
                  text: '选择选项',
                  trigger: DropdownTrigger.click, // 可选hover或click
                  child: Icon(Icons.more_vert, size: iconSize),
                  onItemSelected: (item) {
                    // 编辑
                    if (item.value == 'edit') {
                      if (isGroup) {
                        widget.editGroup?.call(group!);
                        // _createGroupDialogStateKey.currentState?.showCreateGroupDialog(
                        //   context,
                        //   type: DialogTypeEmun.edit,
                        //   // id: group!.id,
                        //   group: group,
                        // );
                      } else {
                        widget.editRole?.call(role!);
                        // _createRoleDialogStateKey.currentState?.showCreateRoleDialog(
                        //   context,
                        //   type: DialogTypeEmun.edit,
                        //   // id: role!.id,
                        //   role: role,
                        // );
                      }
                    }

                    // 删除
                    if (item.value == 'delete') {
                      showDialog(
                        context: context,
                        builder:
                            (ctx) => AlertDialog(
                              title: Text('确认删除'),
                              content: Text('是否确认删除？'),
                              actions: [
                                TextButton(onPressed: () => Navigator.pop(ctx), child: Text('取消')),
                                TextButton(
                                  onPressed: () async {
                                    if (isGroup) {
                                      var postDatas = {'id': group!.id};
                                      await RoleAuthManagementApi.del(postDatas);
                                    } else {
                                      var postDatas = {'id': role!.id};
                                      print('调用删除接口参数：$postDatas');
                                      await RoleAuthManagementApi.delRole(postDatas);
                                    }
                                    getList();
                                    Navigator.pop(ctx);
                                  },
                                  child: Text('删除'),
                                ),
                              ],
                            ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

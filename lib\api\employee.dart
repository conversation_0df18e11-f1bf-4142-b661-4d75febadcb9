import 'package:flutter/material.dart';
import 'package:octasync_client/utils/http_service.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/providers/user_provider.dart';
import 'package:provider/provider.dart';

final _http = HttpService();

const controller = '/Business/Employee/';

class EmployeeApi {
  /// 登录
  static Future<dynamic> login(data) {
    return _http.post('${controller}Login', data: data);
  }

  /// 获取人员部门树状
  static Future<dynamic> getTreeList() {
    return _http.get('${controller}GetTreeList');
  }

  /// 退出登录
  static Future<void> logOut(BuildContext context) async {
    final router = GoRouter.of(context);
    await Provider.of<UserProvider>(context, listen: false).clearUserInfo();
    router.go('/login');
  }

  /// 创建人员
  static Future<dynamic> add(data) {
    return _http.post('${controller}Add', data: data);
  }

  static Future<dynamic> getListPage(data) {
    return _http.post('${controller}GetListPage', data: data);
  }

  static Future<dynamic> delete(data) {
    return _http.post('${controller}Delete', data: data);
  }
}

import 'package:json_annotation/json_annotation.dart';

part 'employee_department.g.dart';

/// 员工部门关联模型
/// 用于表示员工与部门之间的关联关系
@JsonSerializable()
class EmployeeDepartment {
  /// 职务(职位)ID
  @JsonKey(name: 'JobTitleId')
  String? jobTitleId;

  /// 职务(职位)名称
  @JsonKey(name: 'JobTitle', defaultValue: '')
  String jobTitle;

  /// 部门ID
  @JsonKey(name: 'DepartmentId')
  String? departmentId;

  /// 部门名称
  @JsonKey(name: 'DepartmentName', defaultValue: '')
  String departmentName;

  /// 主要部门标识 1.是主要部门 2.不是主要部门
  @JsonKey(name: 'Mainenum', defaultValue: 2)
  int mainEnum;

  /// 构造函数
  EmployeeDepartment({
    this.jobTitleId,
    this.jobTitle = '',
    this.departmentId,
    this.departmentName = '',
    this.mainEnum = 2,
  });

  factory EmployeeDepartment.fromJson(Map<String, dynamic> json) =>
      _$EmployeeDepartmentFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeDepartmentToJson(this);

  /// 获取主要部门状态文本
  String get mainDepartmentText {
    switch (mainEnum) {
      case 1:
        return '主要部门';
      case 2:
        return '非主要部门';
      default:
        return '未知';
    }
  }

  /// 是否为主要部门
  bool get isMainDepartment => mainEnum == 1;

  /// 是否为非主要部门
  bool get isNotMainDepartment => mainEnum == 2;
}

import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_config.g.dart';

@JsonSerializable(explicitToJson: true)
class GroupConfig {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String name;

  /// 系统权限分组: 1.系统权限分组 10.项目权限分组
  @<PERSON>son<PERSON><PERSON>(name: 'GroupTypeenum')
  final int groupTypeenum;

  /// 系统配置 1.是 2.不是
  @Json<PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  @Json<PERSON>ey(name: 'RoleList', defaultValue: <Role>[])
  List<Role>? roleList;

  /// 是否展开（用于UI）
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool isExpanded;

  /// 是否显示（用于UI）
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool isVisible;

  GroupConfig({
    this.id,
    this.name = '',
    this.groupTypeenum = 1,
    this.defaultenum = 2,
    this.roleList,
    this.isExpanded = true,
    this.isVisible = false,
  });

  factory GroupConfig.fromJson(Map<String, dynamic> json) => _$GroupConfigFromJson(json);

  Map<String, dynamic> toJson() => _$GroupConfigToJson(this);
}
